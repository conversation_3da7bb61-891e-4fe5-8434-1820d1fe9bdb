import React, { useEffect, useState } from "react";
import Layout from "../Layout/Layout";
import axios from "axios";
import { FaClock, FaTimes, FaHeart, FaRegHeart } from "react-icons/fa";
const RECENTLY_VIEWED_KEY = "recentlyViewedMeals";

function getDietaryBadges(meal) {
  const BADGE_MAP = [
    { key: 'Vegan', label: 'Vegan', color: '#4CAF50', icon: '🌱' },
    { key: 'Vegetarian', label: 'Vegetarian', color: '#8BC34A', icon: '🥬' },
    { key: 'Flexitarian', label: 'Flexitarian', color: '#A3C9A8', icon: '🤝' },
    { key: 'Dairy-Free', label: 'Dairy-Free', color: '#00BCD4', icon: '🥛🚫' },
    { key: 'Egg-Free', label: 'Egg-Free', color: '#FFEB3B', icon: '🥚🚫' },
    { key: 'Gluten-Free', label: 'Gluten-Free', color: '#FF9800', icon: '🌾' },
    { key: 'Soy-Free', label: 'Soy-Free', color: '#9E9E9E', icon: '🌱🚫' },
    { key: 'Nut-Free', label: 'Nut-Free', color: '#795548', icon: '🥜🚫' },
    { key: 'Low-Carb', label: 'Low-Carb', color: '#9C27B0', icon: '🥩' },
    { key: 'Low-Sugar', label: 'Low-Sugar', color: '#607D8B', icon: '🍬⬇️' },
    { key: 'Sugar-Free', label: 'Sugar-Free', color: '#607D8B', icon: '🍬🚫' },
    { key: 'Low-Fat', label: 'Low-Fat', color: '#03A9F4', icon: '🥗' },
    { key: 'Low-Sodium', label: 'Low-Sodium', color: '#B0BEC5', icon: '🧂⬇️' },
    { key: 'Organic', label: 'Organic', color: '#388E3C', icon: '🍃' },
    { key: 'Halal', label: 'Halal', color: '#2196F3', icon: '☪️' },
    { key: 'High-Protein', label: 'High-Protein', color: '#E91E63', icon: '💪' },
    { key: 'Pescatarian', label: 'Pescatarian', color: '#00B8D4', icon: '🐟' },
    { key: 'Keto', label: 'Keto', color: '#FFB300', icon: '🥓' },
    { key: 'Plant-Based', label: 'Plant-Based', color: '#43A047', icon: '🌿' },
    { key: 'Kosher', label: 'Kosher', color: '#3F51B5', icon: '✡️' },
    { key: 'Climatarian', label: 'Climatarian', color: '#689F38', icon: '🌎' },
    { key: 'Raw Food', label: 'Raw Food', color: '#AED581', icon: '🥗' },
    { key: 'Mediterranean', label: 'Mediterranean', color: '#00ACC1', icon: '🌊' },
    { key: 'Paleo', label: 'Paleo', color: '#A1887F', icon: '🍖' },
    { key: 'Kangatarian', label: 'Kangatarian', color: '#D84315', icon: '🦘' },
    { key: 'Pollotarian', label: 'Pollotarian', color: '#FBC02D', icon: '🍗' },
  ];
  const tags = meal.dietaryTags || [];
  return BADGE_MAP.filter(badge =>
    tags.some(tag => tag.toLowerCase() === badge.key.toLowerCase())
  );
}
// Convert price range to peso signs
const getPesoSigns = (priceRange) => {
  switch (priceRange) {
    case "Low": return "₱";
    case "Mid": return "₱₱";
    case "High": return "₱₱₱";
    default: return "₱";
  }
};

const History = () => {
  const [recentMeals, setRecentMeals] = useState([]);
  const [mealsFromSavedPlans, setMealsFromSavedPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshingRecent, setRefreshingRecent] = useState(false);
  const [refreshingSaved, setRefreshingSaved] = useState(false);
  const [clearingRecent, setClearingRecent] = useState(false);
  const [clearingSaved, setClearingSaved] = useState(false);
  const [error, setError] = useState(null);
  const [showMealDetails, setShowMealDetails] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState(null);
  const [favorites, setFavorites] = useState([]);
  const token = localStorage.getItem("token");

  // Function to load recently viewed meals
  const loadRecentlyViewedMeals = async (showRefreshingState = false) => {
    if (!token) {
      console.log('No token available for loading recently viewed meals');
      return;
    }

    if (showRefreshingState) {
      setRefreshingRecent(true);
    }

    try {
      console.log('=== LOADING RECENTLY VIEWED MEALS (WEBSITE) ===');
      console.log('Token:', token ? 'exists' : 'missing');

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(`${API_BASE_URL}/users/recently-viewed-meals`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('Website API response:', JSON.stringify(response.data, null, 2));

      const meals = response.data.recentlyViewedMeals || [];
      console.log('Setting recent meals count:', meals.length);
      console.log('Recent meals:', JSON.stringify(meals.map(m => ({ name: m.name, id: m.id || m._id })), null, 2));

      setRecentMeals(meals);
      setError(null); // Clear any previous errors
    } catch (error) {
      console.error('Error loading recently viewed meals:', error);
      console.error('Error details:', JSON.stringify(error.response?.data || error, null, 2));
      setRecentMeals([]);

      if (showRefreshingState) {
        let errorMessage = 'Failed to refresh recently viewed meals. ';
        if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
          errorMessage += 'Please check your internet connection.';
        } else if (error.response?.status === 401) {
          errorMessage += 'Please log in again.';
        } else if (error.response?.status === 500) {
          errorMessage += 'Server error. Please try again later.';
        } else {
          errorMessage += 'Please try again.';
        }
        setError(errorMessage);
      }
    } finally {
      if (showRefreshingState) {
        setRefreshingRecent(false);
      }
    }
  };

  // Function to load recently added to meal plans
  const loadMealsFromSavedPlans = async (showRefreshingState = false) => {
    if (!token) return;

    if (showRefreshingState) {
      setRefreshingSaved(true);
    }

    try {
      console.log('=== LOADING RECENTLY ADDED TO MEAL PLANS (WEBSITE) ===');
      console.log('Token:', token ? 'exists' : 'missing');

      const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
      const response = await axios.get(`${API_BASE_URL}/users/recently-added-to-meal-plans`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('Website API response:', JSON.stringify(response.data, null, 2));

      const meals = response.data.recentlyAddedToMealPlans || [];
      console.log('Setting recently added to meal plans count:', meals.length);
      console.log('Recently added meals:', JSON.stringify(meals.map(m => ({ name: m.name, addedToDate: m.addedToDate, addedToMealType: m.addedToMealType })), null, 2));

      setMealsFromSavedPlans(meals);
      setError(null); // Clear any previous errors
    } catch (error) {
      console.error('❌ Error loading recently added to meal plans:', error);
      console.error('❌ Error details:', error.response?.data || error.message);
      setMealsFromSavedPlans([]);

      if (showRefreshingState) {
        let errorMessage = 'Failed to refresh recently added to meal plans. ';
        if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
          errorMessage += 'Please check your internet connection.';
        } else if (error.response?.status === 401) {
          errorMessage += 'Please log in again.';
        } else if (error.response?.status === 500) {
          errorMessage += 'Server error. Please try again later.';
        } else {
          errorMessage += 'Please try again.';
        }
        setError(errorMessage);
      }
    } finally {
      if (showRefreshingState) {
        setRefreshingSaved(false);
      }
    }
  };

  // Function to load all history data
  const loadHistoryData = async () => {
    setLoading(true);
    await Promise.all([
      loadRecentlyViewedMeals(),
      loadMealsFromSavedPlans()
    ]);
    setLoading(false);
  };

  // Load data on mount
  useEffect(() => {
    loadHistoryData();
    loadFavorites();
  }, [token]);

  // Auto-clear error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  // Clear recently viewed meals
  const clearRecentlyViewed = async () => {
    if (window.confirm('Are you sure you want to clear all recently viewed meals? This action cannot be undone.')) {
      try {
        setClearingRecent(true);
        console.log('🗑️ Starting to clear recently viewed meals...');

        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const response = await axios.delete(`${API_BASE_URL}/users/recently-viewed-meals`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        console.log('🗑️ Backend response:', response.data);

        // Clear the frontend state
        setRecentMeals([]);
        setError(null);

        // Also clear localStorage to prevent meals from coming back
        localStorage.removeItem(RECENTLY_VIEWED_KEY);
        localStorage.removeItem('recentlyViewedMeals'); // Alternative key name
        console.log('🗑️ Also cleared localStorage for recently viewed meals');

        console.log('✅ Recently viewed meals cleared successfully');
        alert('Recently viewed meals cleared successfully!');

      } catch (error) {
        console.error('❌ Error clearing recently viewed meals:', error);
        console.error('❌ Error details:', error.response?.data || error.message);
        setError('Failed to clear recently viewed meals: ' + (error.response?.data?.message || error.message));
        alert('Failed to clear recently viewed meals. Please try again.');
      } finally {
        setClearingRecent(false);
      }
    }
  };

  // Clear meals from saved plans
  const clearMealsFromSavedPlans = async () => {
    if (window.confirm('Are you sure you want to clear all meals from saved plans history? This action cannot be undone.')) {
      try {
        setClearingSaved(true);
        console.log('🗑️ Starting to clear recently added to meal plans...');

        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const response = await axios.delete(`${API_BASE_URL}/users/recently-added-to-meal-plans`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        console.log('🗑️ Backend response:', response.data);

        // Clear the frontend state
        setMealsFromSavedPlans([]);
        setError(null);

        console.log('✅ Recently added to meal plans cleared successfully');
        alert('Recently added to meal plans history cleared successfully!');

      } catch (error) {
        console.error('❌ Error clearing recently added to meal plans:', error);
        console.error('❌ Error details:', error.response?.data || error.message);
        setError('Failed to clear recently added to meal plans: ' + (error.response?.data?.message || error.message));
        alert('Failed to clear recently added to meal plans. Please try again.');
      } finally {
        setClearingSaved(false);
      }
    }
  };

  // View meal details
  const viewMealDetails = (meal) => {
    setSelectedMeal(meal);
    setShowMealDetails(true);
  };

  // Close meal details
  const closeMealDetails = () => {
    setShowMealDetails(false);
    setSelectedMeal(null);
  };

  // Load favorites from localStorage
  const loadFavorites = () => {
    try {
      const savedFavorites = localStorage.getItem('favoriteMeals');
      if (savedFavorites) {
        setFavorites(JSON.parse(savedFavorites));
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
      setFavorites([]);
    }
  };

  // Check if a meal is favorite
  const isFavorite = (mealId) => {
    return favorites.some(fav => (fav.id || fav._id) === mealId);
  };

  // Toggle favorite status
  const toggleFavorite = (e, meal) => {
    e.stopPropagation();
    const mealId = meal.id || meal._id;

    let updatedFavorites;
    if (isFavorite(mealId)) {
      // Remove from favorites
      updatedFavorites = favorites.filter(fav => (fav.id || fav._id) !== mealId);
    } else {
      // Add to favorites
      updatedFavorites = [...favorites, meal];
    }

    setFavorites(updatedFavorites);
    localStorage.setItem('favoriteMeals', JSON.stringify(updatedFavorites));
  };

  // Add event listener for when user returns to this page
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, refresh the data
        loadHistoryData();
      }
    };

    const handleFocus = () => {
      // Window gained focus, refresh the data
      loadHistoryData();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [token]);

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format meal type for display
  const formatMealType = (mealType) => {
    return mealType.charAt(0).toUpperCase() + mealType.slice(1);
  };

  return (
    <Layout>
      <div className="main-content">
        {/* Error Display */}
        {error && (
          <div style={{
            backgroundColor: '#ffebee',
            color: '#c62828',
            padding: '12px 16px',
            borderRadius: '4px',
            marginBottom: '20px',
            border: '1px solid #ffcdd2',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <span>{error}</span>
            <button
              onClick={() => setError(null)}
              style={{
                background: 'none',
                border: 'none',
                color: '#c62828',
                cursor: 'pointer',
                fontSize: '18px',
                padding: '0 4px'
              }}
            >
              ×
            </button>
          </div>
        )}

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h1>Family Meal History</h1>
          <button
            onClick={loadHistoryData}
            disabled={loading}
            style={{
              padding: '10px 20px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.6 : 1
            }}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>

          {/* Meals from Saved Plans Section */}
          <div className="history-section">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
              <h2>Recently Added to Family Meal Plans</h2>
              <div style={{ display: 'flex', gap: '10px' }}>
                <button
                  onClick={() => loadMealsFromSavedPlans(true)}
                  disabled={refreshingSaved}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: refreshingSaved ? '#ccc' : '#1890ff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: refreshingSaved ? 'not-allowed' : 'pointer',
                    opacity: refreshingSaved ? 0.6 : 1,
                    fontSize: '12px',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {refreshingSaved ? 'Refreshing...' : 'Refresh'}
                </button>
                <button
                  onClick={clearMealsFromSavedPlans}
                  disabled={clearingSaved}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: clearingSaved ? '#ccc' : '#ff4d4f',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: clearingSaved ? 'not-allowed' : 'pointer',
                    opacity: clearingSaved ? 0.6 : 1,
                    fontSize: '12px',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {clearingSaved ? 'Clearing...' : 'Clear History'}
                </button>
              </div>
            </div>
            {mealsFromSavedPlans.length === 0 ? (
              <p>No meals from saved plans yet.</p>
            ) : (
              <div className="food-grid">
                {mealsFromSavedPlans.map((meal, index) => (
<div key={`${meal.name}-${index}`} className="food-card meal-plan-card">
  <div className="food-card-image">
    {meal.image ? (
      <img src={meal.image} alt={meal.name} />
    ) : (
      <div className="meal-placeholder">🍽️</div>
    )}
  </div>
  <div className="food-card-content">
    {/* 1. Meal Name */}
    <h3 className="food-card-title">{meal.name}</h3>
    {/* 2. Meta row: Calories, Prep Time, Rating */}
    <div className="food-card-meta">
      {meal.calories && (
        <div className="meta-item calories-tag">
          <span>{meal.calories} cal</span>
        </div>
      )}
      {meal.prepTime && (
        <div className="meta-item prep-time-tag">
          <FaClock /> <span>{meal.prepTime} min</span>
        </div>
      )}
      <div className="meta-item rating">
        <span>{meal.rating && meal.rating > 0 ? meal.rating : 3.5} &#9733;</span>
      </div>
    </div>
    {/* 3. Dietary Tags */}
    {getDietaryBadges(meal).length > 0 && (
      <div className="food-card-tags">
        {getDietaryBadges(meal).map((badge, idx) => (
          <span
            key={idx}
            className="dietary-tag"
            style={{
              background: badge.color,
              color: '#fff',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.25em',
            }}
            title={badge.label}
          >
            <span className="dietary-tag-icon">{badge.icon}</span>
            <span className="dietary-tag-label">{badge.label}</span>
          </span>
        ))}
      </div>
    )}
    {/* 4. Description (optional, if you want) */}
    {meal.description && (
      <div className="food-card-description">
        <span>
          {meal.description.length > 80
            ? meal.description.slice(0, 80) + "..."
            : meal.description}
        </span>
      </div>
    )}
    {/* 5. Meal Plan Info */}
    <div className="meal-plan-info">
      <div className="meal-plan-details">
        <span className={`meal-type-badge meal-type-${meal.addedToMealType?.toLowerCase()}`}>
          {formatMealType(meal.addedToMealType)}
        </span>
        <span className="added-date">
          Added {formatDate(meal.addedAt)}
        </span>
      </div>
      <div className="planned-for">
        Planned for: {new Date(meal.addedToDate).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        })}
      </div>
      {meal.planName && (
        <div className="plan-name" style={{
          fontSize: '0.85em',
          color: '#666',
          fontStyle: 'italic',
          marginTop: '4px'
        }}>
          From: {meal.planName}
        </div>
      )}
      {/* View Meal Button centered below plan name */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        marginTop: '12px'
      }}>
        <button
          className="view-meal-btn"
          onClick={() => viewMealDetails(meal)}
          style={{
            padding: '8px 16px',
            fontSize: '14px',
            borderRadius: '6px',
            backgroundColor: '#007AFF',
            color: 'white',
            border: 'none',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
        >
          <span>View Meal</span>
        </button>
      </div>
    </div>
  </div>
</div>
                ))}
              </div>
            )}
          </div>

          {/* Recently Viewed Meals Section */}
          <div className="history-section">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
              <h2>Recently Viewed Meals</h2>
              <div style={{ display: 'flex', gap: '10px' }}>
                <button
                  onClick={() => loadRecentlyViewedMeals(true)}
                  disabled={refreshingRecent}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: refreshingRecent ? '#ccc' : '#1890ff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: refreshingRecent ? 'not-allowed' : 'pointer',
                    opacity: refreshingRecent ? 0.6 : 1,
                    fontSize: '12px',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {refreshingRecent ? 'Refreshing...' : 'Refresh'}
                </button>
                <button
                  onClick={clearRecentlyViewed}
                  disabled={clearingRecent}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: clearingRecent ? '#ccc' : '#ff4d4f',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: clearingRecent ? 'not-allowed' : 'pointer',
                    opacity: clearingRecent ? 0.6 : 1,
                    fontSize: '12px',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {clearingRecent ? 'Clearing...' : 'Clear History'}
                </button>
              </div>
            </div>
            {loading ? (
              <p>Loading recently viewed meals...</p>
            ) : recentMeals.length === 0 ? (
              <p>No recently viewed meals yet. Try viewing some meals first!</p>
            ) : (
              <div className="food-grid">
                {recentMeals.map((meal, index) => (
                  <div key={`${meal.name}-${index}`} className="food-card meal-plan-card">
                    <div className="food-card-image">
                      <img src={meal.image} alt={meal.name} />
                    </div>
                    <div className="food-card-content">
                      {/* 1. Meal Name */}
                      <h3 className="food-card-title">{meal.name}</h3>
                      {/* 2. Meta row: Calories, Prep Time, Rating */}
                      <div className="food-card-meta">
                        {meal.calories && (
                          <div className="meta-item calories-tag">
                            <span>{meal.calories} cal</span>
                          </div>
                        )}
                        {meal.prepTime && (
                          <div className="meta-item prep-time-tag">
                            <FaClock /> <span>{meal.prepTime} min</span>
                          </div>
                        )}
                        <div className="meta-item rating">
                          <span>{meal.rating && meal.rating > 0 ? meal.rating : 3.5} &#9733;</span>
                        </div>
                      </div>
                      {/* 3. Dietary Tags */}
                      {getDietaryBadges(meal).length > 0 && (
                        <div className="food-card-tags">
                          {getDietaryBadges(meal).map((badge, idx) => (
                            <span
                              key={idx}
                              className="dietary-tag"
                              style={{
                                background: badge.color,
                                color: '#fff',
                                display: 'inline-flex',
                                alignItems: 'center',
                                gap: '0.25em',
                              }}
                              title={badge.label}
                            >
                              <span className="dietary-tag-icon">{badge.icon}</span>
                              <span className="dietary-tag-label">{badge.label}</span>
                            </span>
                          ))}
                        </div>
                      )}
                      {/* 4. Description (optional, if you want) */}
                      {meal.description && (
                        <div className="food-card-description">
                          <span>
                            {meal.description.length > 80
                              ? meal.description.slice(0, 80) + "..."
                              : meal.description}
                          </span>
                        </div>
                      )}
                      {/* 5. Recently Viewed Info */}
                      <div className="meal-plan-info">
                        <div className="meal-plan-details">
                          <span className="meal-type-badge meal-type-viewed">
                            Recently Viewed
                          </span>
                          {meal.category && (
                            <span className="added-date">
                              Category: {meal.category}
                            </span>
                          )}
                        </div>
                        {meal.priceRange && (
                          <div className="planned-for">
                            Price Range: {getPesoSigns(meal.priceRange)}
                          </div>
                        )}
                      </div>
                      {/* View Meal Button in footer center */}
                      <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        marginTop: 'auto',
                        paddingTop: '12px',
                        borderTop: '1px solid #f0f0f0'
                      }}>
                        <button
                          className="view-meal-btn"
                          onClick={() => viewMealDetails(meal)}
                          style={{
                            padding: '8px 16px',
                            fontSize: '14px',
                            borderRadius: '6px',
                            backgroundColor: '#007AFF',
                            color: 'white',
                            border: 'none',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <span>View Meal</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
      </div>

      {/* Meal Details Modal */}
      {showMealDetails && selectedMeal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{selectedMeal.name}</h2>
              <button className="close-modal" onClick={closeMealDetails}>
                <FaTimes />
              </button>
            </div>
            <div className="modal-body">
              <div className="meal-image">
                {selectedMeal.image ? (
                  <img src={selectedMeal.image} alt={selectedMeal.name} />
                ) : (
                  <div className="meal-placeholder">🍽️</div>
                )}
              </div>
              <div className="meal-details">
                <p className="meal-description">
                  {selectedMeal.description}
                </p>
                <div className="meal-meta">
                  <span className="meal-rating">
                    {selectedMeal.rating && Number(selectedMeal.rating) > 0 ? selectedMeal.rating : 3.5} &#9733;
                  </span>
                  <span className="meal-category">
                    {selectedMeal.category}
                  </span>
                  <span className="meal-price">
                    Calories: {selectedMeal.calories} (
                    {selectedMeal.priceRange} Range)
                  </span>
                </div>
                {selectedMeal.ingredients && selectedMeal.ingredients.length > 0 && (
                  <div className="meal-ingredients">
                    <h3>Ingredients</h3>
                    <ul className="ingredients-list">
                      {selectedMeal.ingredients.map((ingredient, idx) => (
                        <li key={idx}>
                          {ingredient}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                <div className="meal-nutrition">
                  <h3>Nutrition Information</h3>
                  <div className="nutrition-grid">
                    <div className="nutrition-item">
                      <span className="nutrition-label">Protein:</span>
                      <span className="nutrition-value">
                        {Math.round(selectedMeal.protein || 0)}g
                      </span>
                    </div>
                    <div className="nutrition-item">
                      <span className="nutrition-label">Carbs:</span>
                      <span className="nutrition-value">
                        {Math.round(selectedMeal.carbs || 0)}g
                      </span>
                    </div>
                    <div className="nutrition-item">
                      <span className="nutrition-label">Fat:</span>
                      <span className="nutrition-value">
                        {Math.round(selectedMeal.fat || 0)}g
                      </span>
                    </div>
                    <div className="nutrition-item">
                      <span className="nutrition-label">Calories:</span>
                      <span className="nutrition-value">
                        {Math.round(selectedMeal.calories || 0)}
                      </span>
                    </div>
                    {selectedMeal.calcium && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Calcium:</span>
                        <span className="nutrition-value">
                          {selectedMeal.calcium} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.phosphorus && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Phosphorus:</span>
                        <span className="nutrition-value">
                          {selectedMeal.phosphorus} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.iron && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Iron:</span>
                        <span className="nutrition-value">
                          {selectedMeal.iron} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.vitaminA && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin A:</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminA} µg
                        </span>
                      </div>
                    )}
                    {selectedMeal.vitaminC && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin C:</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminC} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.vitaminB1 && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin B1 (Thiamine):</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminB1} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.vitaminB2 && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin B2 (Riboflavin):</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminB2} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.vitaminB3 && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin B3 (Niacin):</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminB3} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.prepTime && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Prep Time:</span>
                        <span className="nutrition-value">
                          {selectedMeal.prepTime} mins
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                {selectedMeal.instructions && selectedMeal.instructions.length > 0 && (
                  <div className="meal-steps">
                    <h3>Cooking Instructions</h3>
                    <ol>
                      {selectedMeal.instructions.map((step, idx) => (
                        <li key={idx}>{step}</li>
                      ))}
                    </ol>
                  </div>
                )}
                {selectedMeal.dietaryTags && selectedMeal.dietaryTags.length > 0 && (
                  <div className="meal-tags">
                    <h3>Dietary Tags</h3>
                    <div className="tags-container">
                      {selectedMeal.dietaryTags.map((tag, idx) => (
                        <span key={idx} className="dietary-tag">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                {selectedMeal.mealType && selectedMeal.mealType.length > 0 && (
                  <div className="meal-types">
                    <h3>Meal Types</h3>
                    <div className="tags-container">
                      {selectedMeal.mealType.map((type, idx) => (
                        <span key={idx} className="meal-type-tag">
                          {type}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="favorite-modal-btn"
                onClick={(e) => toggleFavorite(e, selectedMeal)}
              >
                {isFavorite(selectedMeal.id || selectedMeal._id) ? (
                  <>
                    Remove from Favorites <FaHeart />
                  </>
                ) : (
                  <>
                    Add to Favorites <FaRegHeart />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default History;