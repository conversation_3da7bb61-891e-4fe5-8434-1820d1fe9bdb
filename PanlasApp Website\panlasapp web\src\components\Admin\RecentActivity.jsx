import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './RecentActivity.css';

function RecentActivity() {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchRecentActivity = async () => {
      try {
        const token = localStorage.getItem('token');
        
        const config = {
          headers: {
            'x-auth-token': token
          }
        };

        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const response = await axios.get(`${API_BASE_URL}/activity/recent`, config);
        setActivities(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to load recent activity');
        console.error('Recent activity error:', err);
        setLoading(false);
      }
    };

    fetchRecentActivity();
  }, []);

  const getActivityIcon = (type) => {
    switch (type) {
      case 'login':
        return '🔑';
      case 'logout':
        return '🚪';
      case 'signup':
        return '👤';
      case 'create_meal_plan':
        return '🍽️';
      case 'update_meal_plan':
        return '📝';
      case 'delete_meal_plan':
        return '🗑️';
      case 'create_meal':
        return '🍳';
      case 'update_meal':
        return '✏️';
      case 'delete_meal':
        return '❌';
      case 'update_profile':
        return '👤';
      case 'made_admin':
        return '👑';
      case 'made_sub_admin':
        return '🛡️';
      case 'made_user':
        return '👥';
      case 'disabled_account':
        return '🚫';
      case 'enabled_account':
        return '✅';
      case 'deleted_feedback':
        return '🗑️';
      case 'submitted_feedback':
        return '💬';
      case 'updated_feedback_status':
        return '📋';
      case 'password_reset':
        return '🔒';
      case 'email_verification':
        return '📧';
      default:
        return '📝';
    }
  };

  if (loading) return <div className="loading-activity">Loading recent activity...</div>;
  if (error) return <div className="error-activity">{error}</div>;

  return (
    <div className="recent-activity">
      <h2>Recent Activity</h2>
      
      {activities.length === 0 ? (
        <div className="no-activity">No recent activity to display</div>
      ) : (
        <ul className="activity-list">
          {activities.map((activity, index) => (
            <li key={index} className="activity-item">
              <div className="activity-icon">
                {getActivityIcon(activity.type)}
              </div>
              <div className="activity-content">
                <div className="activity-user">
                  {activity.username || activity.email || 'Unknown User'}
                  {activity.email && activity.username !== activity.email && (
                    <span className="activity-email"> ({activity.email})</span>
                  )}
                </div>
                <div className="activity-description">{activity.description || `Performed action: ${activity.type || activity.action}`}</div>
                <div className="activity-time">
                  {activity.timestamp || activity.createdAt
                    ? new Date(activity.timestamp || activity.createdAt).toLocaleString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true
                      })
                    : 'Invalid Date'
                  }
                </div>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default RecentActivity;
