const axios = require('axios');

// Test the recent activity endpoint
async function testRecentActivityEndpoint() {
  try {
    console.log('Testing Recent Activity Endpoint...\n');

    // You'll need to replace this with a valid admin token
    const adminToken = 'your-admin-token-here';
    
    const API_BASE_URL = 'http://localhost:5000/api';
    
    const config = {
      headers: {
        'x-auth-token': adminToken
      }
    };

    // Test the recent activity endpoint
    console.log('Fetching recent activities...');
    const response = await axios.get(`${API_BASE_URL}/activity/recent`, config);
    
    console.log('Response status:', response.status);
    console.log('Number of activities:', response.data.length);
    
    if (response.data.length > 0) {
      console.log('\nSample activity:');
      const activity = response.data[0];
      console.log({
        type: activity.type,
        action: activity.action,
        username: activity.username,
        description: activity.description,
        timestamp: activity.timestamp,
        createdAt: activity.createdAt,
        formattedDate: new Date(activity.timestamp || activity.createdAt).toLocaleString()
      });
      
      console.log('\nAll activities:');
      response.data.forEach((activity, index) => {
        console.log(`${index + 1}. ${activity.username} - ${activity.description} - ${new Date(activity.timestamp || activity.createdAt).toLocaleString()}`);
      });
    } else {
      console.log('No activities found');
    }

  } catch (error) {
    console.error('Error testing endpoint:', error.response?.data || error.message);
  }
}

// Test data structure validation
function validateActivityStructure(activity) {
  const requiredFields = ['type', 'username', 'description', 'timestamp'];
  const missingFields = requiredFields.filter(field => !activity[field]);
  
  if (missingFields.length > 0) {
    console.log(`❌ Missing fields: ${missingFields.join(', ')}`);
    return false;
  }
  
  // Test timestamp validity
  const date = new Date(activity.timestamp);
  if (isNaN(date.getTime())) {
    console.log('❌ Invalid timestamp');
    return false;
  }
  
  console.log('✅ Activity structure is valid');
  return true;
}

// Run the test
testRecentActivityEndpoint();
