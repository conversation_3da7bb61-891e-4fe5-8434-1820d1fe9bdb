const Activity = require('../models/Activity');
const User = require('../models/User');
const ActivityService = require('../services/activityService');

// Helper function to get human-readable activity descriptions
const getActivityDescription = (action, details) => {
  switch (action) {
    case 'login':
      return 'Logged into the system';
    case 'logout':
      return 'Logged out of the system';
    case 'signup':
      return 'Created a new account';
    case 'create_meal_plan':
      return `Created meal plan for ${details.date || 'unknown date'}`;
    case 'update_meal_plan':
      return `Updated meal plan for ${details.date || 'unknown date'}`;
    case 'delete_meal_plan':
      return `Deleted meal plan for ${details.date || 'unknown date'}`;
    case 'create_meal':
      return `Added meal: ${details.mealName || 'Unknown meal'}`;
    case 'update_meal':
      return `Updated meal: ${details.mealName || 'Unknown meal'}`;
    case 'delete_meal':
      return `Deleted meal: ${details.mealName || 'Unknown meal'}`;
    case 'update_profile':
      return 'Updated profile information';
    case 'made_admin':
      return `Promoted ${details.targetUser || 'user'} to Admin`;
    case 'made_sub_admin':
      return `Promoted ${details.targetUser || 'user'} to Sub-Admin`;
    case 'made_user':
      return `Changed ${details.targetUser || 'user'} role to User`;
    case 'disabled_account':
      return `Disabled account for ${details.targetUser || 'user'}`;
    case 'enabled_account':
      return `Enabled account for ${details.targetUser || 'user'}`;
    case 'deleted_feedback':
      return `Deleted feedback: ${details.feedbackSubject || 'Unknown feedback'}`;
    case 'submitted_feedback':
      return `Submitted feedback: ${details.feedbackSubject || 'Unknown feedback'}`;
    case 'updated_feedback_status':
      return `Updated feedback status to ${details.newStatus || 'unknown'}`;
    case 'password_reset':
      return 'Reset password';
    case 'email_verification':
      return 'Verified email address';
    default:
      return `Performed action: ${action}`;
  }
};

exports.logActivity = async (req, res) => {
  try {
    const { action, details } = req.body;
    const userId = req.user.id;

    // Get IP address from request
    const ipAddress = req.ip ||
                     req.connection.remoteAddress ||
                     req.socket.remoteAddress ||
                     req.connection.socket.remoteAddress;

    // Create new activity log
    const activity = new Activity({
      user: userId,
      action,
      details,
      ipAddress
    });

    await activity.save();

    res.status(201).json(activity);
  } catch (error) {
    console.error('Activity log error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get activity for a specific user (admin only)
exports.getUserActivity = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    const userId = req.params.userId;

    // Verify user exists
    const userExists = await User.exists({ _id: userId });
    if (!userExists) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get all activity for the user
    const activities = await Activity.find({ user: userId })
      .sort({ createdAt: -1 });

    res.json(activities);
  } catch (error) {
    console.error('Get user activity error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get recent activity for all users (admin only)
exports.getRecentActivity = async (req, res) => {
  try {
    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    // Get limit from query params or default to 50
    const limit = parseInt(req.query.limit) || 50;

    // Get recent activity
    const activities = await Activity.find()
      .populate('user', 'username email')
      .sort({ createdAt: -1 })
      .limit(limit);

    // Format activities for frontend
    const formattedActivities = activities.map(activity => ({
      _id: activity._id,
      type: activity.action, // Map action to type for frontend compatibility
      action: activity.action,
      username: activity.user ? activity.user.username : 'Unknown User',
      email: activity.user ? activity.user.email : 'Unknown Email',
      description: getActivityDescription(activity.action, activity.details),
      timestamp: activity.createdAt, // Map createdAt to timestamp for frontend compatibility
      createdAt: activity.createdAt,
      details: activity.details,
      ipAddress: activity.ipAddress
    }));

    res.json(formattedActivities);
  } catch (error) {
    console.error('Get recent activity error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Get activity log with pagination and filtering (admin only)
exports.getActivityLog = async (req, res) => {
  try {
    console.log('📊 Admin requesting activity logs...');
    console.log('Query params:', req.query);
    console.log('User:', req.user.email);

    // Check if user is admin
    if (!req.user.isAdmin) {
      return res.status(403).json({ message: 'Access denied. Admin only.' });
    }

    // Get query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const action = req.query.action;
    const user = req.query.user;
    const dateFrom = req.query.dateFrom;
    const dateTo = req.query.dateTo;

    // Build filter object
    const filter = {};
    if (action && action !== 'all') {
      filter.action = action;
    }

    // Add date range filter
    if (dateFrom || dateTo) {
      filter.createdAt = {};
      if (dateFrom) {
        filter.createdAt.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        filter.createdAt.$lte = new Date(dateTo + 'T23:59:59.999Z');
      }
    }

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Build aggregation pipeline
    const pipeline = [
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userInfo'
        }
      },
      {
        $unwind: '$userInfo'
      },
      {
        $match: {
          ...filter,
          ...(user ? {
            $or: [
              { 'userInfo.username': { $regex: user, $options: 'i' } },
              { 'userInfo.email': { $regex: user, $options: 'i' } }
            ]
          } : {})
        }
      },
      {
        $project: {
          action: 1,
          details: 1,
          ipAddress: 1,
          createdAt: 1,
          username: '$userInfo.username',
          email: '$userInfo.email',
          type: '$action',
          timestamp: '$createdAt',
          description: {
            $switch: {
              branches: [
                { case: { $eq: ['$action', 'login'] }, then: 'User logged in' },
                { case: { $eq: ['$action', 'logout'] }, then: 'User logged out' },
                { case: { $eq: ['$action', 'create_meal_plan'] }, then: 'Created a meal plan' },
                { case: { $eq: ['$action', 'update_meal_plan'] }, then: 'Updated a meal plan' },
                { case: { $eq: ['$action', 'delete_meal_plan'] }, then: 'Deleted a meal plan' },
                { case: { $eq: ['$action', 'update_profile'] }, then: 'Updated profile' }
              ],
              default: '$action'
            }
          }
        }
      },
      {
        $sort: { createdAt: -1 }
      }
    ];

    // Get total count for pagination
    const totalCountPipeline = [...pipeline, { $count: 'total' }];
    const totalResult = await Activity.aggregate(totalCountPipeline);
    const total = totalResult.length > 0 ? totalResult[0].total : 0;

    // Add pagination to pipeline
    pipeline.push({ $skip: skip }, { $limit: limit });

    // Execute aggregation
    const activities = await Activity.aggregate(pipeline);

    console.log(`✅ Found ${activities.length} activities (total: ${total})`);
    console.log('Sample activity:', activities[0]);

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);

    res.json({
      activities,
      currentPage: page,
      totalPages,
      totalActivities: total,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1
    });
  } catch (error) {
    console.error('Get activity log error:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Test endpoint to create sample activities (for debugging)
exports.createTestActivity = async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    console.log('🧪 Creating test activities for user:', req.user.id);

    // Create multiple test activities
    const testActivities = [];

    // Test login activity
    await ActivityService.logLogin(req.user.id, req.user.email || '<EMAIL>', req);
    testActivities.push('login');

    // Test profile update activity
    await ActivityService.logProfileUpdate(req.user.id, { firstName: 'Test', lastName: 'User' }, req);
    testActivities.push('update_profile');

    // Test meal plan activities
    await ActivityService.logMealPlanCreate(req.user.id, {
      date: '2024-01-15',
      mealType: 'breakfast',
      mealName: 'Test Breakfast'
    }, req);
    testActivities.push('create_meal_plan');

    await ActivityService.logMealPlanUpdate(req.user.id, {
      date: '2024-01-15',
      mealType: 'lunch',
      mealName: 'Test Lunch'
    }, req);
    testActivities.push('update_meal_plan');

    await ActivityService.logMealPlanDelete(req.user.id, '2024-01-14', req);
    testActivities.push('delete_meal_plan');

    console.log('✅ Test activities created:', testActivities);

    // Verify activities were created
    const totalActivities = await Activity.countDocuments({ user: req.user.id });
    console.log(`📊 Total activities in database for user: ${totalActivities}`);

    res.json({
      message: 'Test activities created successfully',
      activities: testActivities,
      count: testActivities.length,
      totalInDatabase: totalActivities
    });
  } catch (error) {
    console.error('❌ Error creating test activities:', error);
    res.status(500).json({ message: 'Failed to create test activities', error: error.message });
  }
};

// Simple endpoint to check database status
exports.checkDatabase = async (req, res) => {
  try {
    const totalActivities = await Activity.countDocuments();
    const totalUsers = await User.countDocuments();
    const recentActivities = await Activity.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('user', 'username email');

    res.json({
      message: 'Database status check',
      stats: {
        totalActivities,
        totalUsers,
        recentActivities: recentActivities.map(activity => ({
          action: activity.action,
          user: activity.user?.email || 'Unknown',
          createdAt: activity.createdAt
        }))
      }
    });
  } catch (error) {
    console.error('❌ Error checking database:', error);
    res.status(500).json({ message: 'Database check failed', error: error.message });
  }
};

// Manual activity creation endpoint (for testing)
exports.createManualActivity = async (req, res) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    console.log('🔧 Creating manual activity for user:', req.user.id);

    // Create a simple activity directly
    const activity = new Activity({
      user: req.user.id,
      action: 'login',
      details: {
        message: 'Manual test activity',
        timestamp: new Date(),
        userAgent: req.headers['user-agent'] || 'Unknown',
        manualTest: true
      },
      ipAddress: req.ip || req.connection?.remoteAddress || 'unknown'
    });

    await activity.save();
    console.log('✅ Manual activity created:', activity._id);

    // Verify it was saved
    const savedActivity = await Activity.findById(activity._id).populate('user', 'username email');

    res.json({
      message: 'Manual activity created successfully',
      activity: {
        id: savedActivity._id,
        action: savedActivity.action,
        details: savedActivity.details,
        user: savedActivity.user,
        createdAt: savedActivity.createdAt
      }
    });
  } catch (error) {
    console.error('❌ Error creating manual activity:', error);
    res.status(500).json({ message: 'Failed to create manual activity', error: error.message });
  }
};
