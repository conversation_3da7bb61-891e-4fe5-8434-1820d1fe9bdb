// Test file for priority assignment logic
// This can be run with Node.js to verify the priority assignment works correctly

// Copy the priority assignment function from feedbackController.js
const determinePriority = (category, subject, message, rating) => {
  // Define base priority for each category
  const categoryPriorities = {
    'bug_report': 'high',
    'technical_issue': 'high', 
    'feature_request': 'medium',
    'user_experience': 'medium',
    'meal_suggestions': 'low',
    'general_feedback': 'low',
    'other': 'medium'
  };

  let priority = categoryPriorities[category] || 'medium';

  // Upgrade priority based on critical keywords in subject or message
  const criticalKeywords = [
    'crash', 'error', 'broken', 'not working', 'urgent', 'critical', 
    'security', 'data loss', 'cannot login', 'payment', 'billing',
    'emergency', 'severe', 'major issue', 'completely broken'
  ];

  const highPriorityKeywords = [
    'slow', 'performance', 'timeout', 'loading', 'freezing',
    'important', 'asap', 'soon', 'priority', 'issue'
  ];

  const content = `${subject} ${message}`.toLowerCase();

  // Check for critical keywords - upgrade to urgent
  if (criticalKeywords.some(keyword => content.includes(keyword))) {
    priority = 'urgent';
  }
  // Check for high priority keywords - upgrade to high (unless already urgent)
  else if (priority !== 'urgent' && highPriorityKeywords.some(keyword => content.includes(keyword))) {
    if (priority === 'low' || priority === 'medium') {
      priority = 'high';
    }
  }

  // Consider rating - very low ratings (1-2) increase priority
  if (rating && rating <= 2) {
    if (priority === 'low') priority = 'medium';
    else if (priority === 'medium') priority = 'high';
    else if (priority === 'high') priority = 'urgent';
  }

  return priority;
};

// Test cases
const testCases = [
  {
    category: 'bug_report',
    subject: 'App crashes on startup',
    message: 'The app crashes every time I try to open it',
    rating: 1,
    expected: 'urgent'
  },
  {
    category: 'feature_request',
    subject: 'Add dark mode',
    message: 'Would love to have a dark mode option',
    rating: 5,
    expected: 'medium'
  },
  {
    category: 'technical_issue',
    subject: 'Cannot login',
    message: 'I cannot login to my account',
    rating: 2,
    expected: 'urgent'
  },
  {
    category: 'meal_suggestions',
    subject: 'More vegetarian options',
    message: 'Please add more vegetarian meal suggestions',
    rating: 4,
    expected: 'low'
  },
  {
    category: 'general_feedback',
    subject: 'Slow performance',
    message: 'The app is running very slow',
    rating: 3,
    expected: 'high'
  },
  {
    category: 'user_experience',
    subject: 'Great app!',
    message: 'Love using this app, very helpful',
    rating: 5,
    expected: 'medium'
  }
];

// Run tests
console.log('Testing Priority Assignment Logic\n');
console.log('='.repeat(50));

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  const result = determinePriority(testCase.category, testCase.subject, testCase.message, testCase.rating);
  const success = result === testCase.expected;
  
  console.log(`Test ${index + 1}: ${success ? 'PASS' : 'FAIL'}`);
  console.log(`  Category: ${testCase.category}`);
  console.log(`  Subject: ${testCase.subject}`);
  console.log(`  Rating: ${testCase.rating}`);
  console.log(`  Expected: ${testCase.expected}`);
  console.log(`  Got: ${result}`);
  console.log('');
  
  if (success) {
    passed++;
  } else {
    failed++;
  }
});

console.log('='.repeat(50));
console.log(`Results: ${passed} passed, ${failed} failed`);

if (failed === 0) {
  console.log('✅ All tests passed!');
} else {
  console.log('❌ Some tests failed!');
}
