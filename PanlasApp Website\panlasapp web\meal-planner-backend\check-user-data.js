require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');

async function checkUserData() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Get all users with their recently added meals
    const users = await User.find({}, 'email username recentlyAddedToMealPlans').limit(10);
    
    console.log(`Checking recently added meals for ${users.length} users:`);
    users.forEach((user, index) => {
      console.log(`\n${index + 1}. User: ${user.email} (${user.username})`);
      console.log(`   Recently added meals: ${user.recentlyAddedToMealPlans?.length || 0}`);
      if (user.recentlyAddedToMealPlans && user.recentlyAddedToMealPlans.length > 0) {
        user.recentlyAddedToMealPlans.forEach((meal, mealIndex) => {
          console.log(`   ${mealIndex + 1}. ${meal.name} - ${meal.calories} cal - ${meal.addedToMealType} on ${meal.addedToDate}`);
        });
      }
    });

    // Close connection
    await mongoose.connection.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

checkUserData();
